<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create Attendance Issue')); ?>

<?php $__env->startSection('css_links'); ?>
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/typography.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/node-waves/node-waves.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/typeahead-js/typeahead.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/jquery-timepicker/jquery-timepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/pickr/pickr-themes.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create Attendance Issue')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Attendance Issues')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Create Attendance Issue')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-7">
        <form action="<?php echo e(route('administration.attendance.issue.store')); ?>" method="POST" id="attendanceIssueForm" autocomplete="off">
            <?php echo csrf_field(); ?>
            <div class="card mb-4">
                <div class="card-header header-elements">
                    <h5 class="mb-0"><?php echo e(__('Assign Attendance Issue')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label for="title" class="form-label"><?php echo e(__('Issue Title')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="title" name="title" value="<?php echo e(old('title')); ?>" placeholder="Forgot To Clockin" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger">
                                    <i class="feather icon-info mr-1"></i>
                                    <?php echo e($message); ?>

                                </b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="attendance_issue_type" class="form-label"><?php echo e(__('Issue For')); ?> <strong class="text-danger">*</strong></label>
                            <div class="row">
                                <div class="col-md mb-md-0 mb-2" title="<?php echo e(__('যদি আপনি ইতিমধ্যেই ক্লক-ইন করে থাকেন, সেই এটেন্ডেন্সের কিছু সমস্যা আছে, এবং তা আপনি ঠিক/আপডেট করাতে চান, তাহলে এই অপশনটি সিলেক্ট করুন।')); ?>">
                                    <div class="form-check custom-option custom-option-basic">
                                        <label class="form-check-label custom-option-content" for="attendanceOld">
                                            <input name="attendance_issue_type" class="form-check-input" type="radio" value="Old" id="attendanceOld" required <?php if(old('attendance_issue_type') == 'Old'): echo 'checked'; endif; ?>/>
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0"><?php echo e(__('Update Existing Attendance')); ?></span>
                                            </span>
                                            <span class="custom-option-body">
                                                <small class="text-muted text-capitalize"><?php echo e(__('If you have clock in/Out data but any of them are incorrect, use this option.')); ?></small>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md">
                                    <div class="form-check custom-option custom-option-basic" title="<?php echo e(__('যদি আপনি ক্লক-ইন করে না থাকেন, এবং সম্পূর্ণ নতুন করে এটেন্ডেন্স রেকর্ড করাতে চান, তাহলে এই অপশনটি সিলেক্ট করুন।')); ?>">
                                        <label class="form-check-label custom-option-content" for="attendanceNew">
                                            <input name="attendance_issue_type" class="form-check-input" type="radio" value="New" id="attendanceNew" required <?php if(old('attendance_issue_type') == 'New'): echo 'checked'; endif; ?>/>
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0"><?php echo e(__('Create New Attendance')); ?></span>
                                            </span>
                                            <span class="custom-option-body">
                                                <small><?php echo e(__('If you have no clock in/out data at all, use this option to request a new attendance')); ?></small>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <?php $__errorArgs = ['attendance_issue_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12" id="newClockInDate">
                            <label for="clock_in_date" class="form-label"><?php echo e(__('Select Clock-In Date')); ?> <strong class="text-danger">*</strong></label>
                            <select name="clock_in_date" id="clock_in_date" class="select2 form-select <?php $__errorArgs = ['clock_in_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" required>
                                <option value="" selected><?php echo e(__('Select Clock-In Date')); ?></option>
                                <?php $__currentLoopData = $dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($date); ?>" <?php echo e(old('clock_in_date') == $date ? 'selected' : ''); ?>>
                                        <?php echo e(show_date($date)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['clock_in_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12" id="oldClockInDate">
                            <label for="attendance_id" class="form-label"><?php echo e(__('Select Attendance')); ?> <strong class="text-danger">*</strong></label>
                            <select name="attendance_id" id="attendance_id" class="select2 form-select <?php $__errorArgs = ['attendance_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" required>
                                <option value="" selected><?php echo e(__('Select Attendance')); ?></option>
                                <?php $__currentLoopData = $attendances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($attendance->id); ?>" <?php echo e(old('attendance_id') == $attendance->id ? 'selected' : ''); ?>>
                                        <?php echo e(show_date_time($attendance->clock_in)); ?> | <?php echo e($attendance->type); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['attendance_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="clock_in" class="form-label"><?php echo e(__('Expected Clockin Time')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="clock_in" name="clock_in" value="<?php echo e(old('clock_in')); ?>" placeholder="YYYY-MM-DD HH:MM" class="form-control date-time-picker <?php $__errorArgs = ['clock_in'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['clock_in'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="clock_out" class="form-label"><?php echo e(__('Expected Clockout Time')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="clock_out" name="clock_out" value="<?php echo e(old('clock_out')); ?>" placeholder="YYYY-MM-DD HH:MM" class="form-control date-time-picker <?php $__errorArgs = ['clock_out'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['clock_out'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="type" class="form-label"><?php echo e(__('Select Clockin Type')); ?> <strong class="text-danger">*</strong></label>
                            <select name="type" id="type" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default" required>
                                <option value="" selected disabled><?php echo e(__('Select Type')); ?></option>
                                <option value="Regular" <?php echo e(old('type') == 'Regular' ? 'selected' : ''); ?>><?php echo e(__('Regular')); ?></option>
                                <option value="Overtime" <?php echo e(old('type') == 'Overtime' ? 'selected' : ''); ?>><?php echo e(__('Overtime')); ?></option>
                            </select>
                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label"><?php echo e(__('Explain Issue Reason')); ?> <strong class="text-danger">*</strong></label>
                            <div name="reason" id="full-editor"><?php echo old('reason'); ?></div>
                            <textarea class="d-none" name="reason" id="reason-input"><?php echo e(old('reason')); ?></textarea>
                            <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <span class="tf-icon ti ti-check ti-xs me-1"></span>
                            <?php echo e(__('Create Issue')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    <!-- Vendors JS -->
    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/jquery-timepicker/jquery-timepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/pickr/pickr.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.date-time-picker').flatpickr({
                enableTime: true,
                dateFormat: 'Y-m-d H:i'
            });
        });

        $(document).ready(function () {
            // Initially hide, disable both fields, and remove required attribute
            $('#newClockInDate, #oldClockInDate').hide().find('select').prop('required', false).prop('disabled', true);

            function toggleClockInDateFields() {
                let selectedType = $('input[name="attendance_issue_type"]:checked').val();

                if (selectedType === 'Old') {
                    $('#oldClockInDate').show().find('select').prop('required', true).prop('disabled', false);
                    $('#newClockInDate').hide().find('select').prop('required', false).prop('disabled', true);
                } else if (selectedType === 'New') {
                    $('#newClockInDate').show().find('select').prop('required', true).prop('disabled', false);
                    $('#oldClockInDate').hide().find('select').prop('required', false).prop('disabled', true);
                } else {
                    // If no selection, hide, disable both, and remove required
                    $('#newClockInDate, #oldClockInDate').hide().find('select').prop('required', false).prop('disabled', true);
                }
            }

            // Bind change event to radio buttons
            $('input[name="attendance_issue_type"]').on('change', toggleClockInDateFields);
        });
    </script>

    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote", "code-block"],
                [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
            ];

            var fullEditor = new Quill("#full-editor", {
                bounds: "#full-editor",
                placeholder: "Ex: Have forgot to bring my ID Card. So Please consider me.",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old reason if validation fails
            <?php if(old('reason')): ?>
                fullEditor.root.innerHTML = <?php echo json_encode(old('reason')); ?>;
            <?php endif; ?>

            $('#attendanceIssueForm').on('submit', function() {
                $('#reason-input').val(fullEditor.root.innerHTML);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/attendance/issue/create.blade.php ENDPATH**/ ?>