<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create New Announcement')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/typography.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create New Announcement')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Announcements')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Create New Announcement')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Create New Announcement</h5>

                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.announcement.index')); ?>" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-circle ti-xs me-1"></span>
                        All Announcements
                    </a>
                </div>
            </div>
            <!-- Account -->
            <div class="card-body">
                <form id="announcementForm" action="<?php echo e(route('administration.announcement.store')); ?>" method="post" enctype="multipart/form-data" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label for="title" class="form-label"><?php echo e(__('Title')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="title" name="title" value="<?php echo e(old('title')); ?>" placeholder="<?php echo e(__('Title')); ?>" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label">Description <strong class="text-danger">*</strong></label>
                            <div name="description" id="full-editor"><?php echo old('description'); ?></div>
                            <textarea class="d-none" name="description" id="description-input"><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="files[]" class="form-label"><?php echo e(__('File(s)')); ?></label>
                            <input type="file" id="files[]" name="files[]" value="<?php echo e(old('files[]')); ?>" placeholder="<?php echo e(__('File(s)')); ?>" class="form-control <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple/>
                            <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="recipients" class="form-label">Select Recipients</label>
                            <select name="recipients[]" id="recipients" class="select2 form-select <?php $__errorArgs = ['recipients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" multiple autofocus>
                                <option value="selectAllValues">Select All</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <optgroup label="<?php echo e($role->name); ?>">
                                        <?php $__currentLoopData = $role->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->id); ?>" <?php echo e(in_array($user->id, old('recipients', [])) ? 'selected' : ''); ?>>
                                                <?php echo e(get_employee_name($user)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </optgroup>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small><b class="text-primary">Note:</b> If the announcement is for all users, then don't select any Recipient.</small>
                            <br>
                            <?php $__errorArgs = ['recipients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="mt-2 float-end">
                        <a href="<?php echo e(route('administration.announcement.create')); ?>" class="btn btn-outline-danger me-2 confirm-danger">Reset Form</a>
                        <button type="submit" class="btn btn-primary">Create Announcement</button>
                    </div>
                </form>
            </div>
            <!-- /Account -->
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>
    <!-- Vendors JS -->
    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote", "code-block"],
                [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
            ];

            var fullEditor = new Quill("#full-editor", {
                bounds: "#full-editor",
                placeholder: "Ex: Mr. John Doe got promoted as Manager",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old description if validation fails
            <?php if(old('description')): ?>
                fullEditor.root.innerHTML = <?php echo json_encode(old('description')); ?>;
            <?php endif; ?>

            $('#announcementForm').on('submit', function() {
                $('#description-input').val(fullEditor.root.innerHTML);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/announcement/create.blade.php ENDPATH**/ ?>